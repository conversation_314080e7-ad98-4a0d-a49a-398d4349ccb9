{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "lint": "npx prettier --check '{**/*,*}.{js,ts,jsx,tsx,php}'", "lint:fix": "npx prettier --write '{**/*,*}.{js,ts,jsx,tsx,php}'"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "tailwindcss": "^4.0.0", "vite": "^6.2.4"}, "dependencies": {"@prettier/plugin-php": "^0.24.0", "prettier": "^3.5.3"}}