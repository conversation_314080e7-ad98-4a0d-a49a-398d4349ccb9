<?php

namespace App\Domain\Google\Actions\Drive;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleSheet;
use App\Domain\Google\Support\Exceptions\GoogleRequestException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class SyncGoogleSheets extends GoogleRequest
{
    public const ENDPOINT = 'https://www.googleapis.com/drive/v3/files';

    public const RESPONSE_FILES = 'files';
    public const RESPONSE_CAPABILITIES = 'capabilities';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_WEB_VIEW_LINK = 'webViewLink';
    public const RESPONSE_CREATED_TIME = 'createdTime';
    public const RESPONSE_MODIFIED_TIME = 'modifiedTime';
    public const RESPONSE_NEXT_PAGE_TOKEN = 'nextPageToken';
    public const RESPONSE_ID = 'id';

    private ?string $pageToken = null;
    private GoogleAccount $account;

    public function execute(GoogleAccount $account, ?string $pageToken = null): ?string
    {
        $this->pageToken = $pageToken;
        $this->account = $account;

        $response = $this->getResponse(RequestType::GET, $account);

        if (!$response->isSuccessful()) {
            throw GoogleRequestException::becauseOfHttpErrorWithStatusCode(
                $account,
                $response->getStatus(),
                $response->getBody(),
            );
        }

        $this->processRows(Arr::get($response->getBody(), self::RESPONSE_FILES, []));

        return Arr::get($response->getBody(), self::RESPONSE_NEXT_PAGE_TOKEN);
    }

    protected function getUrl(): string
    {
        return self::ENDPOINT;
    }

    protected function getQueryParameters(): ?array
    {
        return [
            'pageToken' => $this->pageToken,
            'q' => 'mimeType="application/vnd.google-apps.spreadsheet"',
            'includeItemsFromAllDrives' => 'true',
            'supportsAllDrives' => 'true',
            'includeTeamDriveItems' => 'true',
            'fields' => 'files(id,name,webViewLink,capabilities,createdTime,modifiedTime),nextPageToken',
            'corpora' => 'allDrives',
        ];
    }

    private function processRows(array $rows): void
    {
        foreach ($rows as $row) {
            GoogleSheet::query()->updateOrCreate(
                [
                    GoogleSheet::PROPERTY_EXTERNAL_ID => Arr::get($row, self::RESPONSE_ID),
                    GoogleSheet::PROPERTY_GOOGLE_ACCOUNT_ID => $this->account->getId(),
                ],
                [
                    GoogleSheet::PROPERTY_NAME => Arr::get($row, self::RESPONSE_NAME),
                    GoogleSheet::PROPERTY_URL => Arr::get($row, self::RESPONSE_WEB_VIEW_LINK),
                    GoogleSheet::PROPERTY_CAPABILITIES => Arr::get($row, self::RESPONSE_CAPABILITIES),
                    GoogleSheet::PROPERTY_EXTERNAL_CREATED_AT => Carbon::parse(
                        Arr::get($row, self::RESPONSE_CREATED_TIME),
                    ),
                    GoogleSheet::PROPERTY_EXTERNAL_UPDATED_AT => Carbon::parse(
                        Arr::get($row, self::RESPONSE_MODIFIED_TIME),
                    ),
                ],
            );
        }
    }
}
