<?php

namespace App\Domain\Google\Actions\Authentication;

use App\Domain\Profile\Models\Account;
use App\Support\Traits\HashChecker;
use <PERSON><PERSON>\Socialite\Facades\Socialite;
use <PERSON><PERSON>\Socialite\Two\GoogleProvider;

class GoogleOAuthRedirectLink
{
    use <PERSON>h<PERSON><PERSON><PERSON>;

    public const OAUTH_DRIVER = 'google';

    public const PARAMETER_ACCESS_TYPE = 'access_type';
    public const PARAMETER_PROMPT = 'prompt';
    public const PARAMETER_STATE = 'state';

    public const STATE_PARAMETER_HASH = 'hash';
    public const STATE_PARAMETER_TIMESTAMP = 'timestamp';
    public const STATE_ACCOUNT_ID = 'account_id';

    public const ACCESS_TYPE = 'offline';
    public const PROMPT = 'consent select_account';

    public const SCOPES = ['https://www.googleapis.com/auth/adwords', 'https://www.googleapis.com/auth/drive'];

    private array $additionalState = [];
    private ?string $redirectUrl = null;
    private array $neededScopes;
    private Account $account;

    public function execute(Account $account): string
    {
        $this->account = $account;

        return $this->getSocialite()->redirect()->getTargetUrl();
    }

    private function getSocialiteState(): string
    {
        $timestamp = now()->unix();

        return json_encode([
            self::STATE_PARAMETER_TIMESTAMP => $timestamp,
            self::STATE_PARAMETER_HASH => $this->createHash($timestamp),
            self::STATE_ACCOUNT_ID => $this->account->getId(),
        ]);
    }

    private function getSocialiteParameters(): array
    {
        return [
            self::PARAMETER_ACCESS_TYPE => self::ACCESS_TYPE,
            self::PARAMETER_PROMPT => self::PROMPT,
            self::PARAMETER_STATE => $this->getSocialiteState(),
        ];
    }

    private function getSocialite(): GoogleProvider
    {
        /** @var GoogleProvider $provider */
        $provider = Socialite::driver(self::OAUTH_DRIVER);

        $provider->scopes(self::SCOPES);
        $provider->with($this->getSocialiteParameters());

        if ($this->redirectUrl) {
            $provider->redirectUrl($this->redirectUrl);
        }

        $provider->stateless();

        return $provider;
    }
}
